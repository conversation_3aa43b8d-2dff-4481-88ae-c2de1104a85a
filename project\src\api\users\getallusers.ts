import apiClient from '../../lib/api';

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface User {
  id: string | null;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNo: string;
  joinDate: string;
  designationId: string | null;
  userStatus: string;
  designationName: string;
  userGender: string;
}

export interface GetAllUsersResponse {
  status: string;
  message: string;
  data: {
    totalPages: number;
    pageSize: number;
    currentPage: number;
    content: User[];
    totalElements: number;
  };
  statusCode: number;
}

export async function getAllUsers(page = 0, size = 10): Promise<GetAllUsersResponse> {
  const response = await apiClient.get<GetAllUsersResponse>(`users/all?page=${page}&size=${size}`);
  return response.data;
}

// Simple interface for all users without pagination
export interface SimpleUser {
  id: string | number;
  userId: string | number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNo?: string;
  designationId?: string | number;
  userStatus?: string;
  designationName?: string;
  userGender?: string;
}

export interface GetAllUsersSimpleResponse {
  status: string;
  message: string;
  data: {
    totalPages: number;
    pageSize: number;
    currentPage: number;
    content: SimpleUser[];
    totalElements: number;
  };
  statusCode: number;
}

// Function to get all users without pagination (for dropdowns)
export async function getAllUsersSimple(): Promise<GetAllUsersSimpleResponse> {
  // Use a large page size to get all users in one call
  // This ensures we get all users for dropdown display
  const url = `${BASE_URL}users/all?size=1000`;
  const response = await apiClient.get<GetAllUsersSimpleResponse>(url);
  return response.data;
}

// Interface for users by designation response (different structure)
export interface GetUsersByDesignationResponse {
  status: string;
  message: string;
  data: SimpleUser[];
  statusCode: number;
}

// Function to get users by designation ID
export async function getUsersByDesignationId(designationId: number): Promise<GetUsersByDesignationResponse> {
  const url = `${BASE_URL}users/designation/${designationId}`;
  const response = await apiClient.get<GetUsersByDesignationResponse>(url);
  return response.data;
}
