import apiClient from "../../lib/api";


// Use VITE_BASE_URL from .env
const BASE_URL = `${import.meta.env.VITE_BASE_URL}projectAllocations`;
const BENCH_URL = `${import.meta.env.VITE_BASE_URL}bench`;

// Interface for view allocations response
export interface AvailablePeriod {
  period: string;
  percentage: number;
  project: string;
  userId: number;
}

export interface ViewAllocationsResponse {
  data: {
    availablePeriods: AvailablePeriod[];
  };
  message: string;
  status: string;
  statusCode: number;
}

export interface ProjectAllocationPayload {
  projectId?: string | number;
  allocations?: Array<{
    employeeId: string | number;
    role: string;
    allocationPercent?: number;
    allocationAvailability?: number;
    startDate?: string;
    allocationStartDate?: string;
    endDate?: string;
    allocationEndDate?: string;
  }>;
  // Alternative structure for individual allocations
  employeeId?: string | number;
  role?: string;
  allocationPercent?: number;
  allocationAvailability?: number;
  startDate?: string;
  endDate?: string;
  // Snake case alternatives
  project_id?: string | number;
  employee_id?: string | number;
  allocation_percent?: number;
  start_date?: string;
  end_date?: string;
  // Nested structure alternatives
  project?: { id: string | number };
  employee?: { id: string | number };
  allocation?: {
    percent: number;
    startDate: string;
    endDate: string;
  };
  // Additional fields that might be required
  id?: string | number;
  createdBy?: string | number;
  updatedBy?: string | number;
  createdAt?: string;
  updatedAt?: string;
  status?: string;
  // Allow any additional properties
  [key: string]: any;
}

export async function postProjectAllocations(payload: ProjectAllocationPayload) {
  try {
    const response = await apiClient.post(BASE_URL, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Project allocation error:', error);

      // Include the actual backend error message if available
      const backendMessage = (error as any)?.response?.data?.message ||
                            (error as any)?.response?.data?.error ||
                            (error as any)?.response?.data;

      // If we have a backend message, use it directly. Otherwise, include status info.
      if (backendMessage && typeof backendMessage === 'string') {
        throw new Error(backendMessage);
      } else {
        throw new Error(`Server error: ${(error as any)?.response?.status} - ${(error as any)?.message || 'Unknown error occurred'}`);
      }
  }
}

// New: GET project allocations by ID
export async function getProjectAllocationsById(projectId: string | number) {
  try {
    const response = await apiClient.get(`${BASE_URL}/project/${projectId}`);
    return response.data;
  } catch (error) {
    console.error('Get project allocations error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to get project allocations: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// New: Filter project allocations by search parameters
export async function filterProjectAllocations(projectId: string | number, filters: {
  name?: string;
  role?: string;
  availability?: string;
}) {
  try {
    const params: any = { projectId };
    if (filters.name) params.name = filters.name;
    if (filters.role) params.role = filters.role;
    if (filters.availability) params.availability = filters.availability;
    
    const response = await apiClient.get(`${BASE_URL}/project/${projectId}/filter`, { params });
    return response.data;
  } catch (error) {
    console.error('Filter project allocations error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to filter project allocations: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Update a specific project allocation by allocationId
export async function updateProjectAllocation(allocationId: string | number, payload: ProjectAllocationPayload) {
  try {
    const mappedPayload = {
      userId: payload.userId ?? payload.employeeId ?? payload.employee_id,
      projectId: payload.projectId ?? payload.project_id ?? payload.project?.id,
      roleId: payload.roleId ?? payload.role,
      allocationPercentage: payload.allocationPercentage ?? payload.allocationPercent ?? payload.allocation_percent ?? payload.allocationAvailability,
      startDate: payload.startDate ?? payload.start_date ?? payload.allocationStartDate ?? payload.allocation?.startDate,
      endDate: payload.endDate ?? payload.end_date ?? payload.allocationEndDate ?? payload.allocation?.endDate,
    };
    const response = await apiClient.put(`${BASE_URL}/${allocationId}`, mappedPayload, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Update project allocation error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to update project allocation: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Delete a specific project allocation by allocationId
export async function deleteProjectAllocation(allocationId: string | number, forceDeallocate: boolean = false) {
  try {
    const url = forceDeallocate 
      ? `${BASE_URL}/${allocationId}?forceDeallocate=true`
      : `${BASE_URL}/${allocationId}`;
      
    const response = await apiClient.delete(url, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Delete project allocation error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to delete project allocation: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Get max available percentage for a user within a period
export async function getMaxAvailablePercentage(userId: string | number, newStart: string, newEnd: string) {
  try {
    const response = await apiClient.get(`${BASE_URL}/max-available-percentage`, {
      params: {
        userId,
        newStart,
        newEnd
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Get max available percentage error:', error);

    // Include the actual backend error message if available
    const backendMessage = (error as any)?.response?.data?.message || (error as any)?.response?.data?.error || (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Server error: ${(error as any)?.response?.status} - ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Fetch developers with roles for a given projectId
export async function getDevelopersWithRolesByProjectId(projectId: number) {
  try {
    const response = await apiClient.get(`${BASE_URL}/project/${projectId}`);
    return response.data;
  } catch (error) {
    console.error('Get developers with roles error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to get developers with roles: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Allocate developer to a module
export async function allocateDeveloperToModule(moduleId: number, projectAllocationId: number) {
  try {
    const response = await apiClient.post(`${import.meta.env.VITE_BASE_URL}allocateModule`, {
      moduleId,
      projectAllocationId
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Allocate developer to module error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to allocate developer to module: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Allocate developer to a submodule
export async function allocateDeveloperToSubModule(moduleId: number, projectAllocationId: number, subModuleId: number) {
  try {
    const response = await apiClient.post(`${import.meta.env.VITE_BASE_URL}allocateModule/subModule`, {
      moduleId,
      projectAllocationId,
      subModuleId
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Allocate developer to submodule error:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Failed to allocate developer to submodule: ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}

// Get view allocations for a specific user
export async function getViewAllocations(userId: string | number): Promise<ViewAllocationsResponse> {
  try {
    const response = await apiClient.get(`${BENCH_URL}/view-allocations/${userId}`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: false
    });
    return response.data;
  } catch (error) {
    console.error('Get view allocations error:', error);

    // Include the actual backend error message if available
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    // If we have a backend message, use it directly. Otherwise, include status info.
    if (backendMessage && typeof backendMessage === 'string') {
      throw new Error(backendMessage);
    } else {
      throw new Error(`Server error: ${(error as any)?.response?.status} - ${(error as any)?.message || 'Unknown error occurred'}`);
    }
  }
}