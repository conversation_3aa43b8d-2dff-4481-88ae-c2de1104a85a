
import apiClient from "../../lib/api";
// Use port 8080 for UserEmail API
const BASE_URL = import.meta.env.VITE_BASE_URL || 'http://localhost:8080/api/v1/';

export interface UserEmailPreferences {
  defectEmailStatus: boolean;
  projectAllocationEmailStatus: boolean;
  moduleAllocationEmailStatus: boolean;
  submoduleAllocationEmailStatus: boolean;
}

export interface UpdateUserEmailPreferencesResponse {
  status: string;
  message: string;
  data?: any;
  statusCode: number;
}

// Function to update user email preferences
export async function updateUserEmailPreferences(
  userId: string | number, 
  preferences: UserEmailPreferences
): Promise<UpdateUserEmailPreferencesResponse> {
  try {
    const url = `${BASE_URL}UserEmail/${userId}`;
    console.log('Updating user email preferences:', { userId, preferences, url });
    
    const response = await apiClient.put<UpdateUserEmailPreferencesResponse>(url, preferences);
    console.log('Update user email preferences response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating user email preferences:', error);
    
    // If the API returned an error response, return it instead of throwing
    if (error.response && error.response.data) {
      console.log('API error response:', error.response.data);
      return error.response.data;
    }
    
    throw error;
  }
}

// Function to get user email preferences
export async function getUserEmailPreferences(
  userId: string | number
): Promise<UpdateUserEmailPreferencesResponse> {
  try {
    const url = `${BASE_URL}UserEmail/${userId}`;
    console.log('Getting user email preferences:', { userId, url });

    const response = await apiClient.get<UpdateUserEmailPreferencesResponse>(url);
    console.log('Get user email preferences response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting user email preferences:', error);

    // If the API returned an error response, return it instead of throwing
    if (error.response && error.response.data) {
      console.log('API error response:', error.response.data);
      return error.response.data;
    }

    throw error;
  }
}
