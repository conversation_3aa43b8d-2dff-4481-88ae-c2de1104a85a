import apiClient from "../../lib/api";

export async function getTestCasesByProjectAndSubmodule(projectId: string | undefined, submoduleId: string) {
  const response = await apiClient.get('testcase/filter', {
    params: { projectId, submoduleId },
  });
  return response.data.data || [];
}

export async function deleteTestCase(testCaseId: string) {
  const response = await apiClient.delete(`testcase/${testCaseId}`, {
    data: {
      request: {
        id: testCaseId
      }
    }
  });
  return response.data;
}

export async function getTestCasesByProjectAndModule(projectId: string | number, moduleId: string | number) {
  const response = await apiClient.get('testcase/filterbymodule', {
    params: { projectId, moduleId },
  });
  return response.data.data || [];
}

// New function for bulk module test case fetching
export async function getTestCasesByBulkModules(projectId: string | number, moduleIds: number[]) {
  const response = await apiClient.get('testcase/filterbulkmodule', {
    params: { 
      projectId, 
      moduleIds: moduleIds.join(',') 
    },
  });
  return response.data.data || [];
}

// New function for bulk submodule test case fetching
export async function getTestCasesByBulkSubmodules(projectId: string | number, submoduleIds: number[]) {
  const response = await apiClient.get('testcase/filterbulksubmodule', {
    params: { 
      projectId, 
      submoduleIds: submoduleIds.join(',') 
    },
  });
  return response.data.data || [];
}

export async function getTestCasesByProjectAndModuleExternal(projectId: string | number, moduleId: string | number) {
  // Use the external API base URL directly
  const BASE_URL = "http://34.56.162.48:8087";
  const response = await apiClient.get(`${BASE_URL}/api/v1/testcase/filterbymodule`, {
    params: { projectId, moduleId },
    headers: { "Content-Type": "application/json" },
  });
  // Return the data array from the response
  return response.data.data || [];
} 
