
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface getProjectAllocationHistoryResponse {
  status: string;
  message: string;
  data: any[];
  statusCode: number;
}

export const getProjectAllocationHistory = (id:number): Promise<getProjectAllocationHistoryResponse> => {
  return apiClient.get<getProjectAllocationHistoryResponse>(
    `${BASE_URL}project-allocation-history/project/${id}`,
  ).then(({ data }: { data: getProjectAllocationHistoryResponse }) => data);
};

// export const getProjectAllocationHistoryByDesignation = (projectId: number, designationId?: string): Promise<getProjectAllocationHistoryResponse> => {
//   if (!designationId || designationId === 'all') {
   
//     return apiClient.get<getProjectAllocationHistoryResponse>(`${BASE_URL}project-allocation-history/project/${projectId}`)
//       .then(({ data }: { data: getProjectAllocationHistoryResponse }) => data);
//   } else {
   
//     let url = `${BASE_URL}project-allocation-history?project=${projectId}&designation=${designationId}`;
//     return apiClient.get<getProjectAllocationHistoryResponse>(url)
//       .then(({ data }: { data: getProjectAllocationHistoryResponse }) => data);
//   }
// };

export const getProjectAllocationHistoryByRole = (projectId: number, roleId?: string): Promise<getProjectAllocationHistoryResponse> => {
  if (!roleId || roleId === 'all') {
    // If no role filter or 'all' selected, get all allocations for the project
    return apiClient.get<getProjectAllocationHistoryResponse>(`${BASE_URL}project-allocation-history/project/${projectId}`)
      .then(({ data }: { data: getProjectAllocationHistoryResponse }) => data);
  } else {
    // Use the new role filter API endpoint
    let url = `${BASE_URL}project-allocation-history/by-role?project=${projectId}&role=${roleId}`;
    return apiClient.get<getProjectAllocationHistoryResponse>(url)
      .then(({ data }: { data: getProjectAllocationHistoryResponse }) => data);
  }
};
