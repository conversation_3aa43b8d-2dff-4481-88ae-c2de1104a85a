{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-select": "^2.2.5", "axios": "^1.10.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^6.22.3", "react-window": "^1.8.7", "reactflow": "^11.11.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}