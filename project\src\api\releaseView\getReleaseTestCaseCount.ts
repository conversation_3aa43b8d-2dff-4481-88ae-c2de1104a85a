
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface ReleaseTestCaseCount {
    releaseId: number;
    releaseName: string;
    releaseDate: string;
    testCaseCount: number;
}

export interface ReleaseTestCaseCountResponse {
    status: string;
    message: string;
    data: ReleaseTestCaseCount[];
    statusCode: number;
}

export const getReleaseTestCaseCount = (projectId: string | number): Promise<ReleaseTestCaseCountResponse> => {
    return apiClient
        .get<ReleaseTestCaseCountResponse>(`${BASE_URL}releases/testcasecount/${projectId}`)
        .then(({ data }) => data);
};

// New API for release test case counts with the specific endpoint
export const getReleaseTestCaseCounts = (projectId: string | number): Promise<ReleaseTestCaseCountResponse> => {
    return apiClient
        .get<ReleaseTestCaseCountResponse>(`${BASE_URL}releasetestcase/release-testcase-counts/${projectId}`)
        .then(({ data }) => data);
};