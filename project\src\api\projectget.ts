import apiClient from "../lib/api";
import { Project } from "../types";

export const getAllProjects = async (): Promise<Project[]> => {
  const response = await apiClient.get<Project[]>('projects');
  return response.data;
};

/**
 * Update a project by ID
 * @param params Object with id and projectData
 */
export async function updateProject(params: { id: string | number, projectData: any }) {
    const { id, projectData } = params;
    const BASE_URL = import.meta.env.VITE_BASE_URL;
    const API_URL = `${BASE_URL}projects/${id}`;
    try {
        // Map frontend form data to backend expected fields
        const payload = {
            projectName: projectData.name,
            projectStatus: projectData.status ? (projectData.status).toUpperCase() : null,
            description: projectData.description,
            startDate: projectData.startDate ? new Date(projectData.startDate).toISOString() : null,
            endDate: projectData.endDate ? new Date(projectData.endDate).toISOString() : null,
            clientName: projectData.clientName,
            country: projectData.clientCountry,
            state: projectData.clientState,
            email: projectData.clientEmail,
            phoneNo: projectData.clientPhone,
            userId: projectData.manager, // Dummy userId for now
        };
        const response = await apiClient.put(API_URL, payload);
        return response.data;
    } catch (error: any) {
        throw new Error(error.response?.data?.message || 'Failed to update project');
    }
}
