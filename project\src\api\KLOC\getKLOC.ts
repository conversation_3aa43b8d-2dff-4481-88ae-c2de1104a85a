
import apiClient from "../../lib/api";

// Interface for defect density data
export interface DefectDensityData {
  kloc: number;
  defects: number;
  defectDensity: number;
}

// Interface for defect density API response
export interface DefectDensityResponse {
  status: string;
  message: string;
  data: DefectDensityData;
  statusCode: number;
}

// Fetch defect density (KLOC and defect count) for a given project
export async function getDefectDensity(projectId: string): Promise<DefectDensityResponse> {
  let baseUrl = import.meta.env.VITE_BASE_URL || "";
  // Remove trailing slash if present to avoid double slashes
  if (baseUrl.endsWith("/")) {
    baseUrl = baseUrl.slice(0, -1);
  }
  const url = `${baseUrl}/dashboard/defect-density/${projectId}`;
  const response = await apiClient.get<DefectDensityResponse>(url);
  return response.data;
}
