/// <reference types="vite/client" />
import apiClient from "../../lib/api";

export interface Modules {
  id: number;
  moduleId: string;
  moduleName: string;
  projectId: string;
  submodules?: Submodule[];
  assignedDevs?: string[];
}

export interface Submodule {
  id: number;
  subModuleId: string;
  getSubModuleName: string;
  moduleId: number;
  assignedDevs?: string[];
  // Additional possible property names for submodule name (for backward compatibility)
  name?: string;
  submoduleName?: string;
  subModule?: string;
  subModuleName?: string;
}

export interface CreateReleaseResponse {
  status: string;
  message: string;
  data: Modules[];
  statusCode: number;
}

/**
 * Fetch modules with submodules by project ID
 * @param projectId - The project ID (string or number)
 * @returns Promise resolving to the API response
 */
export const getModulesByProjectId = (projectId: string | number): Promise<CreateReleaseResponse> => {
  return apiClient.get<CreateReleaseResponse>(
    `projects/${projectId}`
  ).then(({ data }: { data: CreateReleaseResponse }) => data);
};


/**
 * Fetch modules by project ID
 * @param projectId - The project ID (string or number)
 * @returns Promise resolving to the API response
 */
// export const getModulesByProjectId = async (projectId: string | number) => {
//   try {
//     const response = await axios.get(`${baseUrl}modules/projectId/${projectId}`);
//     return response.data;
//   } catch (error: any) {
//     // Optionally, you can return a consistent error object
//     return {
//       status: "failure",
//       message: error?.response?.data?.message || error.message || "Unknown error",
//       data: null,
//       statusCode: error?.response?.data?.statusCode || 4000,
//     };
//   }
// };

// Fetch allocated users for a module
export async function getAllocatedUsersByModuleId(moduleId: string | number) {
  const url = `${import.meta.env.VITE_BASE_URL}allocateModule/developers/module/${moduleId}`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch allocated users');
  }
  return response.json();
}

// Fetch users by allocation for module and submodule (for defect assignment)
export async function getUsersByAllocation(projectId: string | number, moduleId: string | number, subModuleId?: string | number) {
  let url = `allocateModule/users-by-allocation?projectId=${projectId}&moduleId=${moduleId}`;

  // Add subModuleId to URL if provided
  if (subModuleId) {
    url += `&subModuleId=${subModuleId}`;
  }

  console.log('Fetching users by allocation from:', url);

  try {
    const response = await apiClient.get(url);
    console.log('Users by allocation response:', response.data);
    return response.data;
  } catch (error: any) {
    throw new Error(`Failed to fetch users by allocation: ${error.response?.status || 'Unknown'} ${error.response?.statusText || error.message}`);
  }
}
