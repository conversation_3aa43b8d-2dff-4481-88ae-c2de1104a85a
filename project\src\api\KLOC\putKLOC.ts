
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

// ProjectDto interface to match backend expectations
export interface ProjectDto {
  kloc: number; // KLOC value
}

// Standard response interface to match backend response
export interface StandardResponse {
  status: string;
  message: string;
  data: any;
  statusCode: number;
}

// Update KLOC and calculate defect density for a project
export const updateProjectKloc = (projectId: number, kloc: number): Promise<StandardResponse> => {
  const projectDto: ProjectDto = { kloc };

  return apiClient.put<StandardResponse>(
    `${BASE_URL}dashboard/defect-density/${projectId}`,
    projectDto,
  ).then(({ data }: { data: StandardResponse }) => data);
};

// Keep the old function for backward compatibility but mark as deprecated
/** @deprecated Use updateProjectKloc instead */
export const createKloc = (payload: { projectId: number; kloc: number }, projectId: number): Promise<StandardResponse> => {
  return updateProjectKloc(projectId, payload.kloc);
};
