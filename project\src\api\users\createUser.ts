import apiClient from "../../lib/api";

export interface CreateUserRequest {
  // userId: string;
  firstName: String;
  lastName: string;
  email: string;
  password: string;
  phoneNo?: BigInteger;
  joinDate?: string;
  userGender?: string;
  userStatus?: string; // Changed from boolean to string to match backend
  designationId: number;
}

export interface CreateUserResponse {
  status: string;
  message: string;
  data: CreateUserRequest[];
  statusCode: number;
}

export const createUser = (payload: CreateUserRequest): Promise<CreateUserResponse> => {
  return apiClient.post<CreateUserResponse>(
    'users',
    payload,
  ).then(({ data }: { data: CreateUserResponse }) => data);
};