
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface UpdateUserPayload {
  id: number;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string | null;
  phoneNo: string;
  joinDate: string;
  userGender: string;
  designationId: number;
  designationName: string;
}

export interface UpdateUserResponse {
  status: string;
  message: string;
  data: any;
  statusCode: number;
}

export interface UpdateUserStatusResponse {
  status: string;
  message: string;
  data: any;
  statusCode: number;
}

export async function updateUser(userId: number, data: UpdateUserPayload) {
  try {
    // Always use the correct user ID (primary key) in the URL
    const response = await apiClient.put(`${BASE_URL}users/${data.id}`, data);
    return response.data;
  } catch (error: any) {
    throw error;
  }
}


export const updateUserStatus = async (userId: number, status: 'Active' | 'Inactive'): Promise<UpdateUserStatusResponse> => {
  const url = `${BASE_URL}users/${userId}/status?status=${status}`;
  const response = await apiClient.patch<UpdateUserStatusResponse>(url);
  return response.data;
};
