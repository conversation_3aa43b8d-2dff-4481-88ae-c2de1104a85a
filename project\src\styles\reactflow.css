.react-flow__node {
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  border-radius: 0 !important;
  background: transparent !important;
  border: none !important;
}

.react-flow__node-custom {
  width: auto !important;
  height: auto !important;
}

.react-flow__handle {
  width: 8px !important;
  height: 8px !important;
  background: #64748b !important;
  border: 2px solid white !important;
}

.react-flow__edge-path {
  stroke: #64748b !important;
  stroke-width: 2 !important;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5 !important;
  animation: flowAnimation 1s linear infinite !important;
}

@keyframes flowAnimation {
  from {
    stroke-dashoffset: 10;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.react-flow__controls {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
}

.react-flow__controls-button {
  border: none !important;
  background: white !important;
  padding: 8px !important;
  color: #64748b !important;
}

.react-flow__controls-button:hover {
  background: #f1f5f9 !important;
}

.react-flow__minimap {
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
} 