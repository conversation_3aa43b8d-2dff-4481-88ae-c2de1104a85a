import apiClient from "../../lib/api";

export interface SearchTestCaseParams {
  description?: string;
  typeId?: number;
  severityId?: number;
  submoduleId?: number;
  moduleId?: number;
}

export async function searchTestCases(params: SearchTestCaseParams) {
  // Remove undefined params
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, v]) => v !== undefined && v !== "")
  );
  const response = await apiClient.get('testcase/search', {
    params: filteredParams,
  });
  return response.data;
}