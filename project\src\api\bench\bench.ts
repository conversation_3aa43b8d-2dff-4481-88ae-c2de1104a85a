import apiClient from "../../lib/api";

import { Employee } from "../../types/index";

const BASE_URL = import.meta.env.VITE_BASE_URL + "bench/search?list";
const API_URL = import.meta.env.VITE_BASE_URL; 

export async function getBenchList(): Promise<Employee[]> {
  try {
    const response = await apiClient.get(BASE_URL, {
      headers: { "Content-Type": "application/json" },
    });

    console.log('Main bench API response:', response.data);

    // Map backend fields to Employee type
    const data = response.data.data || [];
    console.log('Main bench data:', data);

    return data.map((item: any) => {
      const [firstName, ...rest] = (item.fullName || '').split(' ');
      const lastName = rest.join(' ');
      return {
        id: String(item.userId),
        firstName: firstName || '',
        lastName: lastName || '',
        email: '', // Not provided by backend
        phone: '', // Not provided by backend
        designation: item.designation || '',
        experience: 0, // Not provided by backend
        joinedDate: item.availablePeriods || '', // Use availablePeriods for display
        skills: [], // Not provided by backend
        currentProjects: item.currentProjectName ? [item.currentProjectName] : [],
        availability: item.availability || 0,
        status: 'active', // Default value// Not provided by backend
        startDate: '', // Not provided by backend
        endDate: '', // Not provided by backend
        createdAt: '',
        updatedAt: '',
      };
    });
  } catch (error) {
    console.error('Error fetching bench list:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    throw new Error(`Failed to fetch bench employees: ${backendMessage || (error as any)?.message || 'Unknown error occurred'}`);
  }
}

// Function to get view allocation data for a specific user
export const getViewAllocation = async (userId: string) => {
  try {
    console.log('Fetching allocation data for user ID:', userId);
    console.log('User ID type:', typeof userId);
    console.log('Full URL being called:', `/bench/view-allocations/${userId}`);

    // Use the proxy configuration from vite.config.ts
    const response = await apiClient.get(`${API_URL}bench/view-allocations/${userId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response ok:', response.status >= 200 && response.status < 300);

    const data = response.data;
    console.log('Raw API response:', data);

    // Check if data has the expected structure
    if (data && data.data && Array.isArray(data.data)) {
      console.log('Data is an array with length:', data.data.length);
      console.log('First item in data:', data.data[0]);
    } else if (data && data.data && data.data.availablePeriods) {
      console.log('Data has availablePeriods with length:', data.data.availablePeriods.length);
      console.log('Available periods:', data.data.availablePeriods);
    } else {
      console.log('Unexpected data structure:', data);
    }

    return data;
  } catch (error) {
    console.error('Error fetching view allocation:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    throw new Error(`Failed to fetch allocation data: ${backendMessage || (error as any)?.message || 'Unknown error occurred'}`);
  }
};

export async function getEmployeeDetails(employeeId: string): Promise<any> {
  try {
    const response = await apiClient.get(`${API_URL}bench/employees/${employeeId}`, {
      headers: { "Content-Type": "application/json" },
    });

    console.log('Employee details API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching employee details:', error);

    // Extract backend error message
    const backendMessage = (error as any)?.response?.data?.message ||
                          (error as any)?.response?.data?.error ||
                          (error as any)?.response?.data;

    throw new Error(`Failed to fetch employee details: ${backendMessage || (error as any)?.message || 'Unknown error occurred'}`);
  }
}

export const getEmployeeProjectHistory = async (userId: string) => {
    try {
        const response = await apiClient.get(`${API_URL}bench/employees/projects/${userId}`, {
            headers: {
                'Content-Type': 'application/json',
            },
        });

        return response.data;
    } catch (error) {
        console.error('Error fetching employee project history:', error);

        // Extract backend error message
        const backendMessage = (error as any)?.response?.data?.message ||
                              (error as any)?.response?.data?.error ||
                              (error as any)?.response?.data;

        throw new Error(`Failed to fetch project history: ${backendMessage || (error as any)?.message || 'Unknown error occurred'}`);
    }
};

// Function to get bench availability data with filtering support
export const getBenchAvailability = async (
    page: number = 0,
    size: number = 5,
    filters: {
        designation?: string;
        minAvailable?: number;
        startDate?: string;
        endDate?: string;
    } = {}
) => {
    try {
        const params: any = {
            page,
            size
        };

        // Add filters to params if provided
        if (filters.designation) {
            params.designation = filters.designation;
        }
        if (filters.minAvailable !== undefined) {
            params.minAvailable = filters.minAvailable;
        }
        if (filters.startDate) {
            params.startDate = filters.startDate;
        }
        if (filters.endDate) {
            params.endDate = filters.endDate;
        }

        console.log('Bench availability API params:', params);

        const response = await apiClient.get(`${API_URL}bench/availability`, {
            headers: {
                'Content-Type': 'application/json',
            },
            params
        });

        console.log('Bench availability API response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error fetching bench availability:', error);

        // Extract backend error message
        const backendMessage = (error as any)?.response?.data?.message ||
                              (error as any)?.response?.data?.error ||
                              (error as any)?.response?.data;

        throw new Error(`Failed to fetch bench availability: ${backendMessage || (error as any)?.message || 'Unknown error occurred'}`);
    }
};
