import apiClient from '../../lib/api';

export interface SearchUserResponse {
  statusCode: number;
  message: string;
  data: SearchUserData[];
}

export interface SearchUserData {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNo: string;
  joinDate: string;
  userGender: string;
  userStatus: string;
  designationId: number;
  designationName?: string;
}

export const searchUsers = async (query: string): Promise<SearchUserResponse> => {
  const trimmedQuery = query.trim();
  try {
    const response = await apiClient.get(`users/search?query=${encodeURIComponent(trimmedQuery)}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to search users');
  }
};

export const searchUsersByEmployeeId = async (employeeId: string): Promise<SearchUserResponse> => {
  return searchUsers(employeeId);
};

export const searchUsersByFirstName = async (firstName: string): Promise<SearchUserResponse> => {
  return searchUsers(firstName);
};

export const searchUsersByLastName = async (lastName: string): Promise<SearchUserResponse> => {
  return searchUsers(lastName);
};

export const searchUsersByFullName = async (fullName: string): Promise<SearchUserResponse> => {
  return searchUsers(fullName);
};
