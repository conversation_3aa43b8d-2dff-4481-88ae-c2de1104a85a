import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface ReleaseByProject {
    id: string;
    releaseId: string;
    releaseName: string;
    description: string;
    releaseStatus: string;
    releaseDate: string; // YYYY-MM-DD
    releaseType: string;
    projectId: number;
    version?: string;
}

export interface ReleasesByProjectResponse {
    message: string;
    data: ReleaseByProject[];
    status: string;
    statusCode: string;
}

export const getReleasesByProject = (projectId: string | number): Promise<ReleasesByProjectResponse> => {
    return apiClient
        .get<ReleasesByProjectResponse>(`${BASE_URL}releases/project/${projectId}`)
        .then(({ data }) => data);
};
