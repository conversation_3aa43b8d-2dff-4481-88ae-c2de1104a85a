
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface DefectCreate {
  description: string;           // Detailed description of the defect
  steps: string;                // Steps to reproduce the defect
  projectId: number;            // Project ID the defect belongs to
  severityId: number;           // Severity level ID
  priorityId: number;           // Priority level ID
  defectStatusId: number;       // Defect status level ID
  typeId: number;              // Defect type ID
  reOpenCount: number;         // Re-open count (starts at 0 for new defects)
  attachment?: string | null;   // Defect related attachment (Optional)
  assignbyId?: number | null;   // User ID who entered the defect (Optional, null for now)
  assigntoId?: number ;   // User ID to assign the defect to (Optional)
  modulesId: number;           // Module level Id
  subModuleId?: number | null;  // Submodule Level Id (Optional)
  releasesId?: number | null;   // Release ID (Optional)
  testCaseRequired?: boolean;   // Whether test case is required for this defect
  // testCaseId?: number | null; // Commented out as per specification
}

export interface DefectCreateProps {
  message: string;
  data: DefectCreate[];
  status: string;
  statusCode: number;
}

export const addDefects = (payload: DefectCreate | FormData): Promise<DefectCreateProps> => {
  const isFormData = typeof FormData !== 'undefined' && payload instanceof FormData;
  return apiClient
    .post<DefectCreateProps>(
      `${BASE_URL}defect`,
      payload as any,
      isFormData
        ? {
            headers: { 'Content-Type': 'multipart/form-data' },
          }
        : undefined
    )
    .then(({ data }) => data)
    .catch((error) => {
      console.error('API call failed:', error);
      throw error;
    });
};
