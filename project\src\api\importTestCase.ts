import apiClient from "../lib/api";

// Test function to verify Authorization header is being added
export const testAuthHeader = async (): Promise<void> => {
  try {
    console.log('Testing Authorization header...');
    // This will trigger the request interceptor and show if Authorization header is added
    await apiClient.get('testcase/test-auth');
    console.log('Test request completed');
  } catch (error) {
    console.log('Test request failed (expected if endpoint doesn\'t exist):', error);
  }
};

export interface ImportTestCaseResponse {
  status: string;
  message: string;
  data: any;
  statusCode: number;
}


export const importTestCases = (formData: FormData, projectId: string | number): Promise<ImportTestCaseResponse> => {
  return apiClient.post<ImportTestCaseResponse>(
    `testcase/import/${projectId}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  ).then(({ data }) => data);
};

export interface ImportDefectResponse {
  status: string;
  message: string;
  data: any;
  statusCode: number;
}

export const importDefects = (formData: FormData, projectId: string | number): Promise<ImportDefectResponse> => {
  return apiClient.post<ImportDefectResponse>(
    `defect/import/${projectId}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      }
    }
  ).then(({ data }) => data);
};