
import apiClient from "../../lib/api";
const BASE_URL = import.meta.env.VITE_BASE_URL || "http://localhost:8080/api/v1/";

export async function getTestCasesByFilter({projectId,releaseId,moduleId,subModuleId}:
   {
  projectId:   number;
  releaseId:   number;
  moduleId?:   number;
  subModuleId?:number;
}) {
  const params = new URLSearchParams();
  params.append("projectId", String(projectId));
  params.append("releaseId", String(releaseId));
  if (moduleId) params.append("moduleId", String(moduleId));
  if (subModuleId) params.append("subModuleId", String(subModuleId));
  
  
  const url = `${BASE_URL}releasetestcase/filtersgroup?${params.toString()}`;
  
  console.log("Calling getTestCasesByFilter with URL:", url);
  console.log("Parameters:", { projectId, releaseId, moduleId, subModuleId });
  
  try {
    const response = await apiClient.get(url);
    console.log("API Response:", response.data);
  
  // Handle the response format from your backend
  if (response.data && response.data.status === "success" && response.data.data) {
    return response.data.data;
  }
  
  // Fallback handling for different response formats
  if (response.data && Array.isArray(response.data)) {
    return response.data;
  }
  
  if (response.data && response.data.data) {
    return response.data.data;
  }
  
  console.warn("Unexpected response format from getTestCasesByFilter:", response.data);
  return [];
  
  } catch (error) {
    console.error("Error in getTestCasesByFilter:", error);
    throw error;
  }
} 