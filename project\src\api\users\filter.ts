
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface UserFilter {
  id?: number;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNo: string;
  joinDate: string;
  userGender: string;
  userStatus: string;
  designationId: number;
}

export interface GetUsersByFilterResponse {
  status: string;
  message: string;
  data: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    users: UserFilter[];
  };
  statusCode: number;
}

export const getUsersByFilter = (gender?: string, status?: string, designationId?: string | number, page: number = 1, size: number = 10): Promise<GetUsersByFilterResponse> => {
  let url = `${BASE_URL}users/filter?`;
  const params = [];
  if (gender) params.push(`userGender=${encodeURIComponent(gender)}`);
  if (status) params.push(`userStatus=${encodeURIComponent(status)}`); // typo matches backend
  if (designationId) params.push(`designationId=${encodeURIComponent(designationId)}`);

  // Add pagination parameters (1-based pagination)
  params.push(`page=${page}`);
  params.push(`size=${size}`);

  url += params.join('&');

  console.log('🌐 Filter API call:', {
    gender,
    status,
    designationId,
    page,
    size,
    finalUrl: url
  });

  return apiClient.get<GetUsersByFilterResponse>(url).then(({ data }) => {
    console.log('🌐 Filter API response:', {
      status: data.status,
      totalItems: data.data?.totalItems,
      totalPages: data.data?.totalPages,
      currentPage: data.data?.currentPage,
      usersCount: data.data?.users?.length,
      sampleUsers: data.data?.users?.slice(0, 3).map(u => ({ name: `${u.firstName} ${u.lastName}`, gender: u.userGender }))
    });
    return data;
  });
};
