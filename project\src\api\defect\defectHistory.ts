import apiClient from "../../lib/api";

export interface DefectHistoryEntry {
  defectId: number;
  assignedByName: string;
  assignedToName: string;
  defectDate: string;
  defectTime: string;
  previousStatus: string;
  defectStatus: string;
  releaseName: string;
  createdBy: string;
}

export interface DefectHistoryResponse {
  status: string;
  message: string;
  data: DefectHistoryEntry[] | null;
  statusCode: number;
}

export async function getDefectHistoryByDefectId(defectId: number): Promise<DefectHistoryEntry[]> {
  try {
    const url = `defecthistory/${defectId}`;
    const response = await apiClient.get<DefectHistoryResponse>(url, { timeout: 10000 });

    if (!response.data) return [];

    const statusLower = response.data.status?.toLowerCase();

    if (statusLower?.includes("success") && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    if (statusLower?.includes("fail") && response.data.message?.toLowerCase().includes("not found")) {
      return [];
    }

    return Array.isArray(response.data.data) ? response.data.data : [];
    
  } catch (err: any) {
    if (err.response?.status === 404) return [];
    if (err.code === "ECONNABORTED") throw new Error("Request timeout.");
    if (err.message?.includes("Network Error")) throw new Error("Network error.");
    throw new Error(err.response?.data?.message || err.message || "Failed to fetch defect history");
  }
}
