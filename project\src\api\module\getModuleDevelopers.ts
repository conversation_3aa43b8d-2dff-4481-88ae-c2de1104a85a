
import apiClient from "../../lib/api";

let BASE_URL = import.meta.env.VITE_BASE_URL ;
// Ensure BASE_URL ends with a slash
if (!BASE_URL.endsWith("/")) {
  BASE_URL = BASE_URL + "/";
}
// Updated function to accept both projectId and moduleId
export const getDevelopersByModuleId = async (projectId: number, moduleId: number) => {
  // Validate parameters
  if (!projectId || !moduleId || isNaN(projectId) || isNaN(moduleId)) {
    console.error('Invalid parameters for getDevelopersByModuleId:', { projectId, moduleId });
    throw new Error('Invalid projectId or moduleId provided');
  }
  
  const url = `${BASE_URL}allocateModule/allocation`;
  console.log('Calling getDevelopersByModuleId with:', { projectId, moduleId, BASE_URL, fullUrl: url });
  console.log('Full request URL will be:', `${url}?projectId=${projectId}&moduleId=${moduleId}`);
  
  try {
    const response = await apiClient.get(
      url,
      {
        params: { projectId: String(projectId), moduleId: String(moduleId) },
        headers: { "Content-Type": "application/json" }
      }
    );
    
    console.log('getDevelopersByModuleId response:', response.data);
    
    // Handle different response structures
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    } else if (response.data && response.data.content && Array.isArray(response.data.content)) {
      return response.data.content;
    } else {
      console.warn('Unexpected response structure:', response.data);
      return [];
    }
  } catch (error: any) {
    console.error('Error in getDevelopersByModuleId:', {
      projectId,
      moduleId,
      error: error.response?.data || error.message,
      status: error.response?.status
    });
    throw error;
  }
};

// Function to get developers allocated to a submodule
export const getDevelopersBySubmoduleId = async (projectId: number, moduleId: number, submoduleId: number) => {
  // Validate parameters
  if (!projectId || !moduleId || !submoduleId || isNaN(projectId) || isNaN(moduleId) || isNaN(submoduleId)) {
    console.error('Invalid parameters for getDevelopersBySubmoduleId:', { projectId, moduleId, submoduleId });
    throw new Error('Invalid projectId, moduleId, or submoduleId provided');
  }
  
  const url = `${BASE_URL}allocateModule/users-by-allocation`;
  console.log('Calling getDevelopersBySubmoduleId with:', { projectId, moduleId, submoduleId, BASE_URL, fullUrl: url });

  try {
    const response = await apiClient.get(
      url,
      {
        params: { subModuleId: String(submoduleId), projectId: String(projectId), moduleId: String(moduleId) },
        headers: { "Content-Type": "application/json" }
      }
    );
    
    console.log('getDevelopersBySubmoduleId response:', response.data);
    
    // Handle different response structures
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    } else if (response.data && response.data.content && Array.isArray(response.data.content)) {
      return response.data.content;
    } else {
      console.warn('Unexpected response structure:', response.data);
      return [];
    }
  } catch (error: any) {
    console.error('Error in getDevelopersBySubmoduleId:', {
      projectId,
      moduleId,
      submoduleId,
      error: error.response?.data || error.message,
      status: error.response?.status
    });
    throw error;
  }
}; 