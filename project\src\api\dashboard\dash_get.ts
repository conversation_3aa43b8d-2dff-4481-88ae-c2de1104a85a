import apiClient from "../../lib/api";

// Interface for time to find defects response
export interface TimeToFindDefectsResponse {
  status: string;
  message: string;
  data: {
    dayNumber: number;
    totalDefects: number;
    periodStart: string;
    periodEnd: string;
  }[];
  statusCode: number;
}

// Interface for time to fix defects response
export interface TimeToFixDefectsResponse {
  projectId: number;
  releaseName: string;
  dailyData: {
    label: string;
    dayNumber: number;
    defectFixedCount: number;
    timeRange: string;
  }[];
  // Alternative structure that might be returned
  data?: {
    label: string;
    dayNumber: number;
    defectFixedCount: number;
    timeRange: string;
  }[];
}

// Fetch defect severity summary for a given project
export async function getDefectSeveritySummary(projectId: string) {
  const url = `dashboard/defect_severity_summary/${projectId}`;
  console.log('Fetching defect severity summary from:', url);
  const response = await apiClient.get(url);
  console.log('Defect severity summary response:', response.data);
  return response.data;
}



// Fetch day-wise defect counts for a release
export async function getReleaseDefectsDaily(projectId: string, releaseName: string) {
  const url = `dashboard/releases/${projectId}/${releaseName}/defects/daily`;
  const response = await apiClient.get(url);
  return response.data;
}

// Fetch day-wise fixed defect counts for a release (using release name)
export async function getReleaseFixedDefectsDaily(projectId: string, releaseName: string) {
  const url = `dashboard/daily-fix/${projectId}/${releaseName}`;
  const response = await apiClient.get(url);

  return response.data;
}

// Fetch day-wise fixed defect counts for a release (using release ID)
export async function getTimeToFixDefectsDaily(projectId: number, releaseId: number) {
  const url = `dashboard/daily-fix/${projectId}/${releaseId}`;
  console.log('Fetching time to fix defects from:', url);
  const response = await apiClient.get(url);
  console.log('Time to fix defects response:', response.data);
  return response.data;
}
