
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface CreateReleaseRequest {
  releaseName: string;
  releaseDate: String; // YYYY-MM-DD, required
  releaseType: string;
  projectId: number;
  releaseStatus?: string; // Optional since it's not in the form
  description?: string; // Optional since it can be empty
  version?: string; // Optional since it can be empty
}

export interface CreateReleaseResponse {
  status: string;
  message: string;
  data: CreateReleaseRequest[];
  statusCode: number;
}

export const createRelease = (payload: CreateReleaseRequest): Promise<CreateReleaseResponse> => {
  return apiClient.post<CreateReleaseResponse>(
    `${BASE_URL}releases`,
    payload,
  ).then(({ data }: { data: CreateReleaseResponse }) => data);
};
