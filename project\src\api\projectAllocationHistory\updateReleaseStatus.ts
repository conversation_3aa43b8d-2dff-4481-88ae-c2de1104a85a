
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface UpdateReleaseStatusResponse {
  message: string;
  status: string;
  statusCode: string;
}

export const updateReleaseStatus = (
  releaseId: number,
  status: 'ACTIVE' | 'HOLD'
): Promise<UpdateReleaseStatusResponse> => {
  const endpoint = status === 'ACTIVE' ? 'activate' : 'hold';
  const url = `${BASE_URL}releases/${endpoint}/${releaseId}`;
  return apiClient.put<UpdateReleaseStatusResponse>(url, {}).then(({ data }) => data);
}; 