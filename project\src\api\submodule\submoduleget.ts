import apiClient from "../../lib/api";

export interface Submodule {
  id: number;
  name: string;
  // Add other fields if needed
}

export interface GetSubmodulesResponse {
  status: string;
  message: string;
  data: Submodule[];
  statusCode: number;
}

export const getSubmodulesByModuleId = (moduleId: number ): Promise<GetSubmodulesResponse> => {
  return apiClient.get<GetSubmodulesResponse>(
    `subModule/${moduleId}`
  ).then(({ data }) => data);
};
