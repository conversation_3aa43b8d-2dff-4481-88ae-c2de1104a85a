
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface Comment {
  id: number;
  comment: string;
  userId: number | string;
  defectId: number | string;
  attachment?: string | null;
  createdAt: string;
  // Add other fields as needed
}

export interface GetCommentsResponse {
  message: string;
  data: Comment[];
  status?: string;
  statusCode?: number;
}

/**
 * Fetch comments for a given defect id
 * @param defectId - The numeric defect id (long)
 * @returns Promise with the API response
 */
export const getCommentsByDefectId = async (
  defectId: number | string
): Promise<GetCommentsResponse> => {
  try {
    console.log('Fetching comments for defectId:', defectId);
    console.log('API URL:', `${BASE_URL}comment/defect/${defectId}`);
    
    const response = await apiClient.get<GetCommentsResponse>(
      `${BASE_URL}comment/defect/${defectId}`
    );
    console.log('Comments API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Comments API error:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    throw new Error(
      error.response?.data?.message || 'Failed to fetch comments'
    );
  }
};
