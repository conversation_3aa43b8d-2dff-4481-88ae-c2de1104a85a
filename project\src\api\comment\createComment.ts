
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

// Request payload for creating a comment
export interface CreateCommentRequest {
  userId: string | number;
  defectId: string | number;
  comment: string;
  attachment?: string | null;
}

// Response type (customize as per backend response)
export interface CreateCommentResponse {
  message: string;
  data?: any; // Optionally, the created comment object
  status?: string;
  statusCode?: number;
}

/**
 * Create a new comment for a defect
 * @param payload - The comment creation payload
 * @returns Promise with the API response
 */
export const createComment = async (
  payload: CreateCommentRequest
): Promise<CreateCommentResponse> => {
  try {
    console.log('Creating comment with payload:', payload);
    console.log('API URL:', `${BASE_URL}comment`);
    
    const response = await apiClient.post<CreateCommentResponse>(
      `${BASE_URL}comment/defect/${payload.defectId}`,
      payload
    );
    console.log('Create comment API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Create comment API error:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    // Optionally, handle error structure as per backend
    throw new Error(
      error.response?.data?.message || 'Failed to create comment'
    );
  }
};
