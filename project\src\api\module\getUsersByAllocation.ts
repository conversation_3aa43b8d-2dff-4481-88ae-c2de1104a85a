
import apiClient from "../../lib/api";

// Use direct URL since proxy is causing 403 errors for this endpoint
const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface UserByAllocation {
  userId: number;
  userName: string;
  userRole?: string;
  userWithRole: string;
  allocateModuleId?: number; // ID from allocate_module table (for module allocations)
  allocationId?: number; // ID from allocation table (for submodule allocations)
  moduleName?: string;
  projectName?: string;
  moduleId?: number;
  projectId?: number;
  subModuleId?: number; // For submodule allocations
}

// Get users by allocation for a specific project and module
export const getUsersByAllocation = async (projectId: number, moduleId: number): Promise<UserByAllocation[]> => {
  // Validate parameters
  if (!projectId || !moduleId || isNaN(projectId) || isNaN(moduleId)) {
    console.error('Invalid parameters for getUsersByAllocation:', { projectId, moduleId });
    throw new Error('Invalid projectId or moduleId provided');
  }

  const url = `${BASE_URL}allocateModule/allocation`;

  console.log('Fetching users by allocation:', { projectId, moduleId, url });

  try {
    const response = await apiClient.get(url, {
      params: {
        projectId: String(projectId),
        moduleId: String(moduleId)
      },
      headers: {
        "Content-Type": "application/json"
      }
    });

    console.log('getUsersByAllocation response:', response.data);

    // Handle different response structures and map to expected format
    let users: any[] = [];

    if (response.data && Array.isArray(response.data)) {
      users = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      users = response.data.data;
    } else if (response.data && response.data.content && Array.isArray(response.data.content)) {
      users = response.data.content;
    } else {
      console.warn('Unexpected response structure for getUsersByAllocation:', response.data);
      return [];
    }

    // Map the response to include userWithRole field and allocateModuleId
    return users.map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      userRole: user.userRole || 'Module Leader', // Default role if not provided
      userWithRole: `${user.userName}-${user.userRole || 'Module Leader'}`,
      allocateModuleId: user.id, // The 'id' field from API is the allocateModuleId
      moduleName: user.moduleName,
      projectName: user.projectName,
      moduleId: user.moduleId,
      projectId: user.projectId
    }));
  } catch (error: any) {
    console.error('Error in getUsersByAllocation:', {
      projectId,
      moduleId,
      error: error.response?.data || error.message,
      status: error.response?.status
    });
    throw error;
  }
};

// Get users by allocation for a specific project, module, and submodule
export const getUsersBySubmoduleAllocation = async (projectId: number, moduleId: number, subModuleId: number): Promise<UserByAllocation[]> => {
  // Validate parameters
  if (!projectId || !moduleId || !subModuleId || isNaN(projectId) || isNaN(moduleId) || isNaN(subModuleId)) {
    console.error('Invalid parameters for getUsersBySubmoduleAllocation:', { projectId, moduleId, subModuleId });
    throw new Error('Invalid projectId, moduleId, or subModuleId provided');
  }

  const url = `${BASE_URL}allocateModule/users-by-allocation`;

  console.log('Fetching users by submodule allocation:', { projectId, moduleId, subModuleId, url });

  try {
    const response = await apiClient.get(url, {
      params: {
        projectId: String(projectId),
        moduleId: String(moduleId),
        subModuleId: String(subModuleId)
      },
      headers: {
        "Content-Type": "application/json"
      }
    });

    console.log('getUsersBySubmoduleAllocation response:', response.data);

    // Handle different response structures and map to expected format
    let users: any[] = [];

    if (response.data && Array.isArray(response.data)) {
      users = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      users = response.data.data;
    } else if (response.data && response.data.content && Array.isArray(response.data.content)) {
      users = response.data.content;
    } else {
      console.warn('Unexpected response structure for getUsersBySubmoduleAllocation:', response.data);
      return [];
    }

    // Map the response to include userWithRole field and allocationId for submodules
    return users.map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      userRole: user.userRole || 'Developer', // Default role for submodule developers
      userWithRole: `${user.userName}-${user.userRole || 'Developer'}`,
      allocationId: user.allocationId || user.id, // The 'allocationId' field from API for submodule allocations
      moduleName: user.moduleName,
      projectName: user.projectName,
      moduleId: user.moduleId,
      projectId: user.projectId,
      subModuleId: user.subModuleId
    }));
  } catch (error: any) {
    console.error('Error in getUsersBySubmoduleAllocation:', {
      projectId,
      moduleId,
      subModuleId,
      error: error.response?.data || error.message,
      status: error.response?.status
    });
    throw error;
  }
};

// Get users by allocation for a specific project, module, and submodule using the specific endpoint
export const getUsersByModuleSubmoduleAllocation = async (projectId: number, moduleId: number, subModuleId: number): Promise<UserByAllocation[]> => {
  // Validate parameters
  if (!projectId || !moduleId || !subModuleId || isNaN(projectId) || isNaN(moduleId) || isNaN(subModuleId)) {
    console.error('Invalid parameters for getUsersByModuleSubmoduleAllocation:', { projectId, moduleId, subModuleId });
    throw new Error('Invalid projectId, moduleId, or subModuleId provided');
  }

  const url = `${BASE_URL}allocateModule/allocation`;

  console.log('Fetching users by module/submodule allocation:', { projectId, moduleId, subModuleId, url });

  try {
    const response = await apiClient.get(url, {
      params: {
        projectId: String(projectId),
        moduleId: String(moduleId),
        submoduleId: String(subModuleId)
      },
      headers: {
        "Content-Type": "application/json"
      }
    });

    console.log('getUsersByModuleSubmoduleAllocation response:', response.data);

    // Handle the specific response structure from the provided endpoint
    let users: any[] = [];

    if (response.data && response.data.status === "success" && response.data.data && Array.isArray(response.data.data)) {
      users = response.data.data;
    } else if (response.data && Array.isArray(response.data)) {
      users = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      users = response.data.data;
    } else {
      console.warn('Unexpected response structure for getUsersByModuleSubmoduleAllocation:', response.data);
      return [];
    }

    // Map the response to include userWithRole field and allocationId for submodules
    return users.map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      userRole: user.userRole || 'Developer', // Default role for submodule developers
      userWithRole: `${user.userName}-${user.userRole || 'Developer'}`,
      allocationId: user.id, // The 'id' field from API response
      moduleName: user.moduleName,
      projectName: user.projectName,
      moduleId: user.moduleId,
      projectId: user.projectId,
      subModuleId: user.subModuleId
    }));
  } catch (error: any) {
    console.error('Error in getUsersByModuleSubmoduleAllocation:', {
      projectId,
      moduleId,
      subModuleId,
      error: error.response?.data || error.message,
      status: error.response?.status
    });
    throw error;
  }
};
