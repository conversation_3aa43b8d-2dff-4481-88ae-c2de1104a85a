import apiClient from "../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

// Define interfaces for the workflow API
export interface WorkflowTransition {
  fromStatusId: number;
  toStatusId: number;
  isInitialStatus: boolean;
}

export interface SaveWorkflowRequest {
  transitions: WorkflowTransition[];
}

export interface SaveWorkflowResponse {
  status: string;
  message: string;
  data?: any;
  statusCode: number;
}

export interface StatusInfo {
  id: number;
  defectStatusName: string;
  colorCode: string;
}

export interface WorkflowTransitionResponse {
  fromStatus: StatusInfo;
  toStatus: StatusInfo;
}

export interface GetAllWorkflowsResponse {
  status: string;
  message: string;
  data: WorkflowTransitionResponse[];
  statusCode: number;
}

export interface NextStatusResponse {
  status: string;
  message: string;
  data: StatusInfo[];
  statusCode: number;
}

// Error handling helper
const handleApiError = (error: any, operation: string): never => {
  console.error(`Error during ${operation}:`, error);
  if (error.response) {
    throw new Error(`${operation} failed: ${error.response.data?.message || error.response.statusText}`);
  } else if (error.request) {
    throw new Error(`${operation} failed: No response from server`);
  } else {
    throw new Error(`${operation} failed: ${error.message}`);
  }
};

// GET - Fetch all workflows
export const getAllWorkflows = async (): Promise<GetAllWorkflowsResponse> => {
  try {
    console.log('Fetching all workflows...');
    const response = await apiClient.get<GetAllWorkflowsResponse>(`${BASE_URL}workflows/all`);
    console.log('All workflows response:', response.data);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, 'fetch all workflows');
  }
};

// POST - Save workflow transitions
export const saveWorkflow = async (workflowData: SaveWorkflowRequest): Promise<SaveWorkflowResponse> => {
  try {
    console.log('Saving workflow with data:', workflowData);
    const response = await apiClient.post<SaveWorkflowResponse>(`${BASE_URL}workflows/save`, workflowData);
    console.log('Workflow save response:', response.data);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, 'save workflow');
  }
};

// GET - Fetch next available statuses for a given status
export const getNextStatuses = async (fromStatusId: number): Promise<NextStatusResponse> => {
  try {
    console.log(`Fetching next statuses for status ID: ${fromStatusId}`);
    const response = await apiClient.get<NextStatusResponse>(`${BASE_URL}workflows/next-statuses?fromStatusId=${fromStatusId}`);
    console.log('Next statuses response:', response.data);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, 'fetch next statuses');
  }
};
