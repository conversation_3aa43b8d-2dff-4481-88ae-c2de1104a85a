import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// Create the main API client instance


// Token management utilities
export const tokenManager = {
  getToken: (): string | null => {
    return localStorage.getItem('authToken');
  },
  
  setToken: (token: string): void => {
    localStorage.setItem('authToken', token);
  },
  
  removeToken: (): void => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  },
  
  isTokenValid: (): boolean => {
    const token = tokenManager.getToken();
    if (!token) return false;

    // If token looks like a JWT, validate expiry. Otherwise, consider it valid if present.
    if (token.includes('.')) {
      try {
        const base64Url = token.split('.')[1];
        if (!base64Url) return true; // treat as valid if we cannot inspect

        // Convert base64url -> base64 and add padding
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const padded = base64.padEnd(Math.ceil(base64.length / 4) * 4, '=');
        const json = atob(padded);
        const payload = JSON.parse(json);

        const currentTimeSeconds = Date.now() / 1000;
        // If exp exists, enforce it. If missing, treat token as valid.
        if (typeof payload.exp === 'number') {
          return payload.exp > currentTimeSeconds;
        }
        return true;
      } catch {
        // If decoding fails (e.g., non-standard token), assume valid rather than logging out users on refresh
        return true;
      }
    }

    // Non-JWT token: assume valid when present
    return true;
  },

  clearAuthData: (): void => {
    tokenManager.removeToken();
    // Clear any other auth-related data
    localStorage.removeItem('user');
    localStorage.removeItem('authToken');
  }
};
const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL || '/api/v1/',
  headers: {
    'Authorization': `Bearer ${tokenManager.getToken()}`,
    'Content-Type': 'application/json',
  },
  timeout: 1000000, // 1000 seconds timeout
});

// Request interceptor to add Authorization header (exclude auth/login)
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Skip adding Authorization header for auth/login requests
    const isAuthLoginRequest = config.url?.includes('/auth/login') ||
                              config.url?.includes('auth/login') ||
                              (config.baseURL?.includes('auth') && config.url?.includes('login'));

    if (!isAuthLoginRequest) {
      const token = tokenManager.getToken();
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
        console.log('API Client: Added Authorization header for request to:', config.url);
        console.log('API Client: Token preview:', token.substring(0, 20) + '...');
      } else {
        console.log('API Client: No token available for request to:', config.url);
      }
    } else {
      console.log('API Client: Skipping Authorization header for auth/login request:', config.url);
    }

    return config;
  },
  (error) => {
    console.error('API Client: Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiry and errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token is invalid or expired
      console.warn('Authentication failed - clearing auth data and redirecting to login');
      tokenManager.clearAuthData();

      // Dispatch a custom event to notify the app about logout
      window.dispatchEvent(new CustomEvent('auth:logout'));

      // Redirect to login page if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    } else if (error.response?.status === 403) {
      // Forbidden - user doesn't have permission
      console.warn('Access forbidden - insufficient permissions');
    }
    return Promise.reject(error);
  }
);

export default apiClient;
