
import apiClient from "../../lib/api";


export interface Designations {
    id: number;
    name: string;
}
export interface CreateDesignationPayload {
    name: string;
}

export interface DesignationGetResponse {
    message: string;
    data: Designations[];
    status: string;
    statusCode: string;
}

export async function createDesignation(payload: CreateDesignationPayload) {
  try {
    const response = await apiClient.post(`designation`, payload);
    return response.data;
  } catch (error: any) {
    throw error;
  }
}

export const getDesignations = (): Promise<DesignationGetResponse> => {
    return apiClient
        .get<DesignationGetResponse>('designation')
        .then(({ data }) => data);
};

export const deleteDesignation = (id: number): Promise<DesignationGetResponse> => {
    return apiClient
        .delete<DesignationGetResponse>(`designation/${id}`)
        .then(({ data }) => data);
};

export const putDesignation = (id: number,data: Partial<Designations>) => {
    return apiClient
        .put<DesignationGetResponse>(`designation/${id}`, {
            ...data
        })
        .then(({ data }) => data);
};