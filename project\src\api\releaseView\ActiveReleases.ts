
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface ActiveRelease {
    id: string;
    releaseId: string;
    releaseName: string;
    description: string;
    releaseStatus: string;
    releaseDate: string; // YYYY-MM-DD
    releaseType: string;
    projectId: number;
}

export interface ActiveReleasesResponse {
    message: string;
    data: ActiveRelease[];
    status: string;
    statusCode: string;
}

export const getActiveReleases = (projectId: string | number): Promise<ActiveReleasesResponse> => {
    return apiClient
        .get<ActiveReleasesResponse>(`${BASE_URL}releases/activeReleases?projectId=${projectId}`)
        .then(({ data }) => data);
}; 