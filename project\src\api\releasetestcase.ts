
import apiClient from "../lib/api";
const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface TestCase {
  id: string;
  module: string;
  subModule: string;
  description: string;
  steps: string;
  type: string;
  severity: string;
  projectId: string;
  releaseId?: string;
  // Alternative field names that might be used by the backend
  testCaseType?: string;
  testCaseSeverity?: string;
  testType?: string;
  testSeverity?: string;
  caseType?: string;
  caseSeverity?: string;
}

export interface GetTestCasesByFilterResponse {
  status: string;
  message: string;
  data: TestCase[];
  statusCode: number;
}

export const getTestCasesByFilter = async (
  projectId: string | number,
  moduleId: string | number,
  submoduleId: string | number,
  releaseId: string | number
): Promise<GetTestCasesByFilterResponse> => {
  const params = new URLSearchParams({
    projectId: String(projectId),
    moduleId: String(moduleId),
    submoduleId: String(submoduleId),
    releaseId: String(releaseId),
  });
  const url = `${BASE_URL}testcase/filter?${params.toString()}`;
  const { data } = await apiClient.get<GetTestCasesByFilterResponse>(url);
  return data;
};

export interface AllocateTestCaseRequest {
  releaseId: number;
  testCaseId:  number;

}

export interface AllocateTestCaseResponse {
  status: string;
  message: string;
  data?: any;
  statusCode: number;
}

export const allocateTestCaseToRelease = async (

  releaseId:  number,
  testCaseId: number,

): Promise<AllocateTestCaseResponse> => {
  const payload: AllocateTestCaseRequest = {
 
    testCaseId: testCaseId,
    releaseId: releaseId,    
   };
  
  const { data } = await apiClient.post<AllocateTestCaseResponse>(
    `${BASE_URL}releasetestcase/allocate`,
    payload
  );
  return data;
};

export interface AllocateOneToManyRequest {
  testCaseId: string | number;
  releaseIds: (string | number)[];
}

export const allocateTestCaseToMultipleReleases = async (
  testCaseId: string | number,
  releaseIds: (string | number)[]
): Promise<AllocateTestCaseResponse> => {
  const payload: AllocateOneToManyRequest = {
    testCaseId: String(testCaseId),
    releaseIds: releaseIds.map(String)
  };
  
  const { data } = await apiClient.post<AllocateTestCaseResponse>(
    `${BASE_URL}releasetestcase/allocate-one-to-many`,
    payload
  );
  return data;
};

// Many-to-many allocation interface
export interface AllocateManyToManyRequest {
  releaseIds: (string | number)[];
  testCaseIds: (string | number)[];
}

export const allocateTestCasesToManyReleases = async (
  releaseIds: (string | number)[],
  testCaseIds: (string | number)[]
): Promise<AllocateTestCaseResponse> => {
  const payload: AllocateManyToManyRequest = {
    releaseIds: releaseIds.map(String),
    testCaseIds: testCaseIds.map(String),
  };
  const { data } = await apiClient.post<AllocateTestCaseResponse>(
    `${BASE_URL}releasetestcase/allocate-many-to-many`,
    payload
  );
  return data;
};


export interface ReleaseTestCaseMappingRequest {
  testCaseId: number;
  releaseId: number;
}

interface ReleaseTestCaseMappingResponse{
  status:string;
  message:string;
  statusCode:number;
  data:any[]
}
// Bulk allocate: { testCaseIds: number[], releaseId: number }
export interface BulkAllocateRequest {
  testCaseIds: (string | number)[];
  releaseId: string | number;
}

export const bulkAllocateTestCasesToReleases = (
  testCaseIds: (string | number)[],
  releaseId: string | number
): Promise<any> => {
  const payload: BulkAllocateRequest = {
    testCaseIds: testCaseIds.map(Number),
    releaseId: Number(releaseId),
  };
  return apiClient.post(`${BASE_URL}releasetestcase/bulk-allocate`, payload)
    .then(({ data }: { data: any }) => data);
};

// API for /releasetestcase/filtersgroup
export const getReleaseTestCasesByFiltersGroup = async ({
  moduleId,
  subModuleId,
  releaseId,
  projectId,
}: {
  moduleId: number;
  subModuleId: number;
  releaseId: number;
  projectId: number;
}) => {
  const url = `${BASE_URL}releasetestcase/filtersgroup`;
  const response = await apiClient.get(url, {
    params: { moduleId, subModuleId, releaseId, projectId },
  });
  return response.data; // Return the actual response data, not the wrapped response
};

// API for QA allocation summary
export interface QaAllocationSummaryResponse {
  status: string;
  message: string;
  data: {
    allocationSummary: {
      totalAllocated: number;
      qaEngineerCount: number;
      remaining: number;
      qaEngineers: Array<{
        id: number;
        name: string;
        testCases: number;
      }>;
    };
  };
  statusCode: number;
}

export const getQaAllocationSummary = async (qaEngineerIds: string): Promise<QaAllocationSummaryResponse> => {
  const url = `${BASE_URL}releasetestcase/qa-allocation-summary`;
  const response = await apiClient.get(url, {
    params: { qaEngineerIds },
  });
  return response.data;
};

// New API for getting detailed test case information for QA engineers
export interface QaEngineerTestCasesResponse {
  status: string;
  message: string;
  data: Array<{
    id: number;
    testCaseId: string;
    description: string;
    steps: string;
    type: string;
    severity: string;
  }>;
  statusCode: number;
}

export const getQaEngineerTestCases = async (
  projectId: number,
  releaseId: number,
  moduleId: number,
  subModuleId: number
): Promise<QaEngineerTestCasesResponse> => {
  const url = `${BASE_URL}releasetestcase/filtersgroup`;
  const response = await apiClient.get(url, {
    params: { projectId, releaseId, moduleId, subModuleId },
  });
  return response.data;
};

// New API for getting defect information for test cases
export interface DefectTestCaseInfo {
  testId: number;
  testCaseId: string;
  defectId: string;
  assignedTo: string;
}

export interface DefectTestCaseCountsResponse {
  status: string;
  message: string;
  data: DefectTestCaseInfo[];
  statusCode: number;
}

export const getDefectTestCaseCounts = async (
  releaseId: string | number
): Promise<DefectTestCaseCountsResponse> => {
  console.log('Calling getDefectTestCaseCounts with releaseId:', releaseId);
  
  // Use the BASE_URL constant that's already defined
  const url = `${BASE_URL}releasetestcase/defects-testcasecounts/${releaseId}`;
  
  // Get the token exactly like Postman would
  const token = localStorage.getItem('authToken');
  console.log('Auth token available:', !!token);
  if (token) {
    console.log('Token preview:', token.substring(0, 20) + '...');
  }
  
  try {
    // Use GET request without body since it's a path parameter
    console.log('Attempting GET request to:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('GET request successful:', data);
    return data;
  } catch (error: any) {
    console.error('GET request failed:', error);
    throw error;
  }
};
