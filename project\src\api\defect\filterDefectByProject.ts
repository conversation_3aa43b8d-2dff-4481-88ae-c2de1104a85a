
import apiClient from "../../lib/api";

const baseUrl = import.meta.env.VITE_BASE_URL;

export interface FilteredDefect {
  id: number;
  defectId: string;
  description: string;
  reOpenCount: number;
  attachment: string | null;
  steps: string;
  // Support both snake_case and camelCase field names from backend
  project_name?: string; projectName?: string;
  severity_name?: string; severityName?: string;
  priority_name?: string; priorityName?: string;
  priority?: string;
  defect_status_name?: string; statusName?: string;
  release_test_case_description?: string;
  release_name?: string; releaseName?: string;
  assigned_by_name?: string; assignedByName?: string;
  assigned_to_name?: string; assignedToName?: string;
  assigned_by_id?: number;
  assigned_to_id?: number;
  defect_type_name?: string; defectTypeName?: string;
  module_name?: string; moduleName?: string;
  sub_module_name?: string; subModuleName?: string;
  testCaseId?: number | null;
}

/**
 * Fetches all defects for a specific project using the project-specific endpoint.
 * Uses the endpoint: /api/v1/defect/project/{projectId}
 *
 * @param projectId The project ID to fetch defects for
 * @returns Promise<FilteredDefect[]>
 */
export async function getDefectsByProjectId(projectId: string | number): Promise<FilteredDefect[]> {
  try {
    const response = await apiClient.get(`${baseUrl}defect/project/${projectId}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    let defects: any[] = [];

    // Try different response structures
    if (response.data && Array.isArray(response.data.data)) {
      // Standard format: { data: [...] }
      defects = response.data.data;
    } else if (response.data && Array.isArray(response.data)) {
      // Direct array format
      defects = response.data;
    } else if (response.data && response.data.defects && Array.isArray(response.data.defects)) {
      // Alternative format: { defects: [...] }
      defects = response.data.defects;
    } else {
      return [];
    }

    return defects;

  } catch (error) {
    console.error('Error fetching defects by project:', error);
    throw error;
  }
}

/**
 * Fetches defects filtered by projectId and optional filters from the backend API.
 * Updated to support the new API format with comma-separated multiple IDs:
 * - /api/v1/?severityIds=1,2&moduleIds=7,4&typeIds=2,7&statusIds=6,4,1&projectId=1
 * - /api/v1/?projectId=1
 * - /api/v1/defect/filter?severityIds=1&projectId=1
 * - /api/v1/defect/filter?priorityIds=1&projectId=1
 * - /api/v1/defect/filter?typeIds=1&projectId=1
 * - /api/v1/defect/filter?statusIds=1&projectId=1
 *
 * @param filters An object with projectId (mandatory) and optional filter arrays
 * @returns Promise<FilteredDefect[]>
 */
export async function filterDefects(filters: {
  projectId: string | number;
  typeIds?: number[];
  severityIds?: number[];
  priorityIds?: number[];
  statusIds?: number[];
  releaseIds?: number[];
  moduleIds?: number[];
  subModuleIds?: number[];
  assignToIds?: number[];
  assignByIds?: number[];

  // Legacy single value support for backward compatibility
  typeId?: number;
  severityId?: number;
  priorityId?: number;
  statusId?: number;
  releaseId?: number;
  moduleId?: number;
  subModuleId?: number;
  assignToId?: number;
  assignById?: number;
}): Promise<FilteredDefect[]> {
  const params: any = { projectId: filters.projectId };

  // Helper function to convert array to comma-separated string
  const arrayToCommaSeparated = (arr: number[] | undefined): string | undefined => {
    return arr && arr.length > 0 ? arr.join(',') : undefined;
  };

  // Map new array-based filter parameters (preferred)
  if (filters.typeIds && filters.typeIds.length > 0) {
    params.typeIds = arrayToCommaSeparated(filters.typeIds);
  }
  if (filters.severityIds && filters.severityIds.length > 0) {
    params.severityIds = arrayToCommaSeparated(filters.severityIds);
  }
  if (filters.priorityIds && filters.priorityIds.length > 0) {
    params.priorityIds = arrayToCommaSeparated(filters.priorityIds);
  }
  if (filters.statusIds && filters.statusIds.length > 0) {
    params.statusIds = arrayToCommaSeparated(filters.statusIds);
  }
  if (filters.releaseIds && filters.releaseIds.length > 0) {
    params.releaseIds = arrayToCommaSeparated(filters.releaseIds);
  }
  if (filters.moduleIds && filters.moduleIds.length > 0) {
    params.moduleIds = arrayToCommaSeparated(filters.moduleIds);
  }
  if (filters.subModuleIds && filters.subModuleIds.length > 0) {
    params.subModuleIds = arrayToCommaSeparated(filters.subModuleIds);
  }
  if (filters.assignToIds && filters.assignToIds.length > 0) {
    params.assignToIds = arrayToCommaSeparated(filters.assignToIds);
  }
  if (filters.assignByIds && filters.assignByIds.length > 0) {
    params.assignByIds = arrayToCommaSeparated(filters.assignByIds);
  }

  // Legacy single value support (convert to array format)
  if (filters.typeId && !params.typeIds) {
    params.typeIds = filters.typeId.toString();
  }
  if (filters.severityId && !params.severityIds) {
    params.severityIds = filters.severityId.toString();
  }
  if (filters.priorityId && !params.priorityIds) {
    params.priorityIds = filters.priorityId.toString();
  }
  if (filters.statusId && !params.statusIds) {
    params.statusIds = filters.statusId.toString();
  }
  if (filters.releaseId && !params.releaseIds) {
    params.releaseIds = filters.releaseId.toString();
  }
  if (filters.moduleId && !params.moduleIds) {
    params.moduleIds = filters.moduleId.toString();
  }
  if (filters.subModuleId && !params.subModuleIds) {
    params.subModuleIds = filters.subModuleId.toString();
  }
  if (filters.assignToId && !params.assignToIds) {
    params.assignToIds = filters.assignToId.toString();
  }
  if (filters.assignById && !params.assignByIds) {
    params.assignByIds = filters.assignById.toString();
  }

  const response = await apiClient.get(`${baseUrl}defect/filter`, {
    params,
    headers: { 'Content-Type': 'application/json' },
  });

  if (response.data && Array.isArray(response.data.data)) {
    return response.data.data;
  }
  return [];
}