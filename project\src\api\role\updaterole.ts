
import apiClient from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface UpdateRoleResponse {
  status: string;
  message: string;
  statusCode: number;
  data?: {
    id: string;
    roleName: string;
  };
}

export async function updateRoleById(Id: string | number, roleName: string): Promise<UpdateRoleResponse> {
  try {
    const response = await apiClient.put(`${BASE_URL}role/${Id}`, { roleName });
    return response.data;
  } catch (error: any) {
    // Handle specific error cases
    if (error.response?.status === 409) {
      // Conflict - role already exists
      throw new Error('Role already exists');
    } else if (error.response?.status === 400) {
      // Bad request - validation error
      throw new Error('Invalid role name format');
    } else if (error.response?.data?.message) {
      // Use the error message from the backend
      throw new Error(error.response.data.message);
    } else {
      // Generic error
      throw new Error('Failed to update role');
    }
  }
}
