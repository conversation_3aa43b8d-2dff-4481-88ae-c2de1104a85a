
import apiClient from "../../lib/api";
import { tokenManager } from "../../lib/api";

const BASE_URL = import.meta.env.VITE_BASE_URL;

export interface CreateTestCaseRequest {

  subModuleId: number | null;
  moduleId: number;
  steps: string;
  severityId: number;
  projectId: number;
  description: string;
  defectTypeId: number;
}

export interface CreateTestCaseResponse {
  status: string;
  message: string;
  data: CreateTestCaseRequest[];
  statusCode: number;
}

export const createTestCase = (payload: CreateTestCaseRequest[]): Promise<CreateTestCaseResponse> => {
  // Debug: Check authentication status before making the request
  const token = tokenManager.getToken();
  const isTokenValid = tokenManager.isTokenValid();
  
  console.log('CreateTestCase Debug Info:');
  console.log('- Token present:', !!token);
  console.log('- Token valid:', isTokenValid);
  console.log('- Token preview:', token ? `${token.substring(0, 20)}...` : 'No token');
  console.log('- Request payload:', payload);
  console.log('- Request URL:', `${BASE_URL}testcase`);

  return apiClient.post<CreateTestCaseResponse>(
    `${BASE_URL}testcase`,
    payload,
  ).then(({ data }: { data: CreateTestCaseResponse }) => data)
  .catch((error: any) => {
    // Provide specific error messages for different scenarios
    if (error.response?.status === 403) {
      throw new Error('Access denied: You do not have permission to create test cases. Please contact your administrator to grant the necessary permissions.');
    } else if (error.response?.status === 401) {
      throw new Error('Authentication required: Please log in again.');
    } else if (error.response?.status === 400) {
      throw new Error(`Invalid request: ${error.response.data?.message || 'Please check your input data.'}`);
    } else if (error.response?.status === 404) {
      throw new Error('Test case endpoint not found. Please check the API configuration.');
    } else if (error.response?.status === 500) {
      throw new Error('Server error: Please try again later or contact support.');
    } else if (error.code === 'ERR_NETWORK') {
      throw new Error('Network error: Unable to connect to the server. Please check your internet connection.');
    } else {
      throw new Error(`Failed to create test case: ${error.response?.data?.message || error.message || 'Unknown error occurred'}`);
    }
  });
};
